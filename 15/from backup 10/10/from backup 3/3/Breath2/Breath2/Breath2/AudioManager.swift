//
//  AudioManager.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import Foundation
import AVFoundation
import Combine

class AudioManager: ObservableObject {
    // MARK: - Published Properties
    @Published var currentFrequency: Double = 0.0
    @Published var currentPressure: Double = 0.0
    @Published var audioLevel: Float = 0.0
    @Published var hasPermission: Bool = false

    // Breath detection
    @Published var breathDetector = BreathDetector()

    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var pitchDetector: PitchDetector?
    private var pressureCalculator: PressureCalculator?
    // Audio configuration
    private var sampleRate: Double = 44100.0
    private var bufferSize: AVAudioFrameCount = 4410 // Default buffer size

    // MARK: - Pipeline State Management (Paper Specification)
    private var totalSamplesProcessed: Int = 0  // Track absolute sample position across chunks

    // MARK: - Initialization
    init() {
        setupAudioComponents()
    }
    
    // MARK: - Public Methods
    func requestPermission() {
        #if os(iOS)
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    self?.hasPermission = granted
                    if granted {
                        self?.setupAudioSession()
                    }
                }
            }
        }
        #endif
    }
    
    func startRecording() {
        print("🎬 startRecording called")
        guard hasPermission else {
            print("❌ Audio permission not granted")
            return
        }

        // Reset breath detection for new session
        breathDetector.reset()

        // Reset pipeline state for new session (Paper specification)
        totalSamplesProcessed = 0

        print("🔧 Setting up audio engine...")
        setupAudioEngine()

        // Start new session as specified in paper
        pitchDetector?.startNewSession()

        do {
            try audioEngine?.start()
            print("✅ Audio engine started successfully")
        } catch {
            print("❌ Failed to start audio engine: \(error)")
        }
    }

    func stopRecording() {
        print("🛑 stopRecording called")
        audioEngine?.stop()
        if let inputNode = audioEngine?.inputNode {
            inputNode.removeTap(onBus: 0)
        }
        print("✅ Audio engine stopped")
    }
    
    // MARK: - Private Methods
    private func setupAudioComponents() {
        // Use default configuration for now
        pitchDetector = PitchDetector(
            sampleRate: Float(sampleRate),
            minFreq: 7,
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4,
            saveResults: false
        )
        pressureCalculator = PressureCalculator()

        // Use default buffer size
        bufferSize = AVAudioFrameCount(sampleRate * 0.1) // 100ms buffer
    }

    // Configuration management temporarily disabled
    
    private func setupAudioSession() {
        #if os(iOS)
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: [])
            print("✅ Audio session configured successfully")
        } catch {
            print("❌ Failed to setup audio session: \(error)")
        }
        #endif
    }
    
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        inputNode = audioEngine?.inputNode

        guard let inputNode = inputNode else {
            print("Failed to get input node")
            return
        }

        let inputFormat = inputNode.outputFormat(forBus: 0)
        print("Input format: \(inputFormat)")

        // Update sample rate based on actual input format
        sampleRate = inputFormat.sampleRate

        // Calculate buffer size for exactly 0.1sec as per paper
        bufferSize = AVAudioFrameCount(sampleRate * 0.1)

        // Recreate pitch detector with correct sample rate
        pitchDetector = PitchDetector(
            sampleRate: Float(sampleRate),
            minFreq: 7,  // Extended range to detect lower frequencies
            maxFreq: 40,
            freqAccuracy: 0.025,
            lowerFormantFreq: 250,
            decayRate: 0.8,
            minAmp: 2.0e-4,
            saveResults: false  // Add the new parameter
        )

        // Install tap to process audio data
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: inputFormat) { [weak self] buffer, time in
            self?.processAudioBuffer(buffer)
        }

        audioEngine?.prepare()
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        print("📊 processAudioBuffer called - frameLength: \(buffer.frameLength)")

        guard let channelData = buffer.floatChannelData?[0] else {
            print("❌ No channel data available")
            return
        }

        guard let pitchDetector = pitchDetector else {
            print("❌ PitchDetector is nil")
            return
        }

        guard let pressureCalculator = pressureCalculator else {
            print("❌ PressureCalculator is nil")
            return
        }

        let frameCount = Int(buffer.frameLength)
        let audioData = Array(UnsafeBufferPointer(start: channelData, count: frameCount))

        // Debug: Check if we're getting audio data
        let audioLevel = audioData.map { abs($0) }.max() ?? 0.0
        print("🎤 Audio Level: \(audioLevel), Samples: \(frameCount)")

        // Process audio chunk through pitch detector with proper sample indexing (Paper specification)
        print("🔍 Processing audio through pitch detector...")
        let detectedPitch = pitchDetector.processChunk(
            audioData,
            thisFirstNativeInd: totalSamplesProcessed,
            numElements: frameCount
        )

        // Update total samples processed for next chunk
        totalSamplesProcessed += frameCount

        print("🎵 Detected pitch: \(detectedPitch) Hz (total samples: \(totalSamplesProcessed))")

        // Convert pitch to pressure using the linear model from the paper
        let calculatedPressure = pressureCalculator.calculatePressure(fromPitch: detectedPitch)
        print("� Calculated pressure: \(calculatedPressure) cm H2O")

        // Update UI on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentFrequency = Double(detectedPitch)
            self?.currentPressure = Double(calculatedPressure)
            self?.audioLevel = audioLevel

            // Process breath detection using pressure and frequency signals
            self?.breathDetector.processBreathingSignals(
                pressure: calculatedPressure,
                frequency: detectedPitch,
                audioLevel: audioLevel
            )

            print("🖥️ UI updated - Freq: \(detectedPitch), Pressure: \(calculatedPressure), Breaths: \(self?.breathDetector.breathCount ?? 0)")
        }
    }

    // MARK: - Result Saving Methods (Paper Specification)

    /// Enable result saving for debugging/research (Paper specification)
    func enableResultSaving(_ enabled: Bool) {
        // Update configuration to enable/disable result saving
        var config = AlgorithmConfiguration.standard
        config.saveResults = enabled
        pitchDetector?.updateConfiguration(config)

        print("💾 Result saving \(enabled ? "enabled" : "disabled")")
    }

    /// Export saved results for analysis (Paper specification)
    func exportResults() -> (detections: Data?, correlations: Data?) {
        return pitchDetector?.exportResults() ?? (nil, nil)
    }

    /// Save results to Documents directory (Paper specification)
    func saveResultsToDocuments() throws {
        guard let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw NSError(domain: "AudioManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not access Documents directory"])
        }

        let resultsURL = documentsURL.appendingPathComponent("PitchDetectionResults")

        // Create directory if it doesn't exist
        try FileManager.default.createDirectory(at: resultsURL, withIntermediateDirectories: true, attributes: nil)

        try pitchDetector?.saveResultsToFiles(baseURL: resultsURL)

        print("💾 Results saved to: \(resultsURL.path)")
    }
}
